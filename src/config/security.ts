/**
 * Security Configuration
 *
 * Centralized security configuration for the application
 * including CSP, security headers, and authentication settings
 */

// Content Security Policy configuration
export const CSP_CONFIG = {
  "default-src": ["'self'"],
  "script-src": ["'self'", "'unsafe-inline'", "'unsafe-eval'"], // Note: Consider removing unsafe-* in production
  "style-src": ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
  "font-src": ["'self'", "https://fonts.gstatic.com"],
  "img-src": ["'self'", "data:", "https:"],
  "connect-src": [
    "'self'",
    import.meta.env.VITE_API_URL || "http://localhost:8000",
    "https://ql8j1nfk-8008.inc1.devtunnels.ms",
  ],
  "frame-ancestors": ["'none'"],
  "base-uri": ["'self'"],
  "form-action": ["'self'"],
  "upgrade-insecure-requests": [],
};

// Security headers configuration
export const SECURITY_HEADERS = {
  "X-Content-Type-Options": "nosniff",
  "X-Frame-Options": "DENY",
  "X-XSS-Protection": "1; mode=block",
  "Referrer-Policy": "strict-origin-when-cross-origin",
  "Permissions-Policy": "camera=(), microphone=(), geolocation=()",
  "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload",
};

// Authentication security settings
export const AUTH_SECURITY = {
  ACCESS_TOKEN_EXPIRY: 15 * 60, // 15 minutes
  REFRESH_TOKEN_EXPIRY: 7 * 24 * 60 * 60, // 7 days
  TOKEN_REFRESH_THRESHOLD: 5 * 60, // Refresh when 5 minutes left
  MAX_LOGIN_ATTEMPTS: 5,
  LOGIN_LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
  SESSION_TIMEOUT: 30 * 60 * 1000, // 30 minutes of inactivity
  REQUIRE_HTTPS: true,
  SECURE_COOKIES: true,
  SAME_SITE_COOKIES: "Strict" as const,
};

// API security settings
export const API_SECURITY = {
  REQUEST_TIMEOUT: 30000, // 30 seconds
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second
  RATE_LIMIT_REQUESTS: 100,
  RATE_LIMIT_WINDOW: 60 * 1000, // 1 minute
  ENABLE_CSRF_PROTECTION: true,
  ALLOWED_ORIGINS: [
    window.location.origin,
    import.meta.env.VITE_API_URL || "http://localhost:8000",
  ],
};

// Security monitoring settings
export const SECURITY_MONITORING = {
  LOG_SECURITY_EVENTS: true,
  LOG_FAILED_ATTEMPTS: true,
  LOG_TOKEN_REFRESH: false, // Set to true for debugging
  ALERT_ON_SUSPICIOUS_ACTIVITY: true,
  MAX_CONCURRENT_SESSIONS: 3,
};

// Environment-specific security settings
export const getEnvironmentSecurity = () => {
  const isDevelopment = import.meta.env.DEV;
  const isProduction = import.meta.env.PROD;

  return {
    REQUIRE_HTTPS: isProduction,
    ENABLE_STRICT_CSP: isProduction,
    LOG_LEVEL: isDevelopment ? "debug" : "error",
    ENABLE_SECURITY_WARNINGS: isDevelopment,
    ALLOW_UNSAFE_EVAL: isDevelopment, // For development tools
    ENABLE_SOURCE_MAPS: isDevelopment,
  };
};

// Security validation functions
export const validateSecurityConfig = (): boolean => {
  const env = getEnvironmentSecurity();

  // Check HTTPS requirement
  if (
    env.REQUIRE_HTTPS &&
    window.location.protocol !== "https:" &&
    window.location.hostname !== "localhost"
  ) {
    console.error("HTTPS is required in production environment");
    return false;
  }

  // Check API URL configuration
  if (!import.meta.env.VITE_API_URL) {
    console.warn("API URL not configured");
  }

  // Check encryption key
  if (
    !import.meta.env.VITE_ENCRYPTION_KEY ||
    import.meta.env.VITE_ENCRYPTION_KEY === "default-key-change-in-production"
  ) {
    if (env.ENABLE_SECURITY_WARNINGS) {
      console.warn("Default encryption key detected. Change in production!");
    }
  }

  return true;
};

// Setup security headers (for client-side enforcement)
export const setupSecurityHeaders = (): void => {
  // Note: These are primarily for documentation and client-side validation
  // Server should set actual HTTP headers

  const meta = document.createElement("meta");
  meta.httpEquiv = "Content-Security-Policy";

  const cspString = Object.entries(CSP_CONFIG)
    .map(([directive, sources]) => `${directive} ${sources.join(" ")}`)
    .join("; ");

  meta.content = cspString;
  document.head.appendChild(meta);

  // Add other security meta tags
  const securityMetas = [
    { httpEquiv: "X-Content-Type-Options", content: "nosniff" },
    { httpEquiv: "X-Frame-Options", content: "DENY" },
    { httpEquiv: "X-XSS-Protection", content: "1; mode=block" },
    { httpEquiv: "Referrer-Policy", content: "strict-origin-when-cross-origin" },
  ];

  securityMetas.forEach(({ httpEquiv, content }) => {
    const meta = document.createElement("meta");
    meta.httpEquiv = httpEquiv;
    meta.content = content;
    document.head.appendChild(meta);
  });
};

// Security event logging
export const logSecurityEvent = (event: string, details?: any): void => {
  if (!SECURITY_MONITORING.LOG_SECURITY_EVENTS) return;

  const logData = {
    timestamp: new Date().toISOString(),
    event,
    details,
    userAgent: navigator.userAgent,
    url: window.location.href,
  };

  console.log("[Security Event]", logData);

  // In production, send to security monitoring service
  if (import.meta.env.PROD) {
    // TODO: Implement security event reporting
    // sendSecurityEvent(logData);
  }
};

// Rate limiting utility
class RateLimiter {
  private requests: Map<string, number[]> = new Map();

  isAllowed(
    key: string,
    limit: number = API_SECURITY.RATE_LIMIT_REQUESTS,
    window: number = API_SECURITY.RATE_LIMIT_WINDOW,
  ): boolean {
    const now = Date.now();
    const requests = this.requests.get(key) || [];

    // Remove old requests outside the window
    const validRequests = requests.filter((time) => now - time < window);

    if (validRequests.length >= limit) {
      return false;
    }

    validRequests.push(now);
    this.requests.set(key, validRequests);

    return true;
  }

  reset(key: string): void {
    this.requests.delete(key);
  }
}

export const rateLimiter = new RateLimiter();

// Security utilities
export const securityUtils = {
  validateSecurityConfig,
  setupSecurityHeaders,
  logSecurityEvent,
  rateLimiter,

  // Generate secure random string
  generateSecureRandom: (length: number = 32): string => {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, "0")).join("");
  },

  // Validate token format
  isValidTokenFormat: (token: string): boolean => {
    return typeof token === "string" && token.length > 10 && /^[A-Za-z0-9._-]+$/.test(token);
  },

  // Check if running in secure context
  isSecureContext: (): boolean => {
    return window.isSecureContext || window.location.hostname === "localhost";
  },
};

export default {
  CSP_CONFIG,
  SECURITY_HEADERS,
  AUTH_SECURITY,
  API_SECURITY,
  SECURITY_MONITORING,
  getEnvironmentSecurity,
  securityUtils,
};
